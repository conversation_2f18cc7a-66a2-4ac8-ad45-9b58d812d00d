import { AIModel, AISubModel, ModelActivity } from '@/types/ai-models'
import { PageTemplate, PageProject, ComponentLibraryItem, PageComponent } from '@/types/page-builder'

// بيانات وهمية للتطوير والاختبار

export interface MockPage {
  id: string
  slug: string
  is_published: boolean
  author_id: string
  featured_image?: string
  created_at: string
  updated_at: string
  page_content: MockPageContent[]
  profiles?: {
    full_name: string
  }
}

export interface MockPageContent {
  id: string
  page_id: string
  language: 'ar' | 'en' | 'fr'
  title: string
  content: string
  meta_description?: string
  meta_keywords?: string
}

export interface MockMenuItem {
  id: string
  title_ar: string
  title_en?: string
  title_fr?: string
  slug: string
  icon?: string
  parent_id?: string
  order_index: number
  is_active: boolean
  target_type: 'internal' | 'external' | 'page'
  target_value: string
  created_at: string
  updated_at: string
}

export interface MockAIProvider {
  id: string
  provider: string
  providerName: string
  baseUrl: string
  apiKey: string
  models: string[]
  description?: string
  status: 'active' | 'inactive'
  createdAt: string
  updatedAt: string
}

export interface MockCategory {
  id: string
  name_ar: string
  name_en?: string
  name_fr?: string
  slug: string
  icon?: string
  description?: string
  is_active: boolean
  order_index: number
  created_at: string
  updated_at: string
}

export interface MockProduct {
  id: string
  name: string
  description: string
  category: string // تغيير من union type إلى string للمرونة
  price: number
  rental_price?: number
  colors: string[]
  sizes: string[]
  images: string[]
  stock_quantity: number
  is_available: boolean
  is_published: boolean // حقل جديد للتحكم في النشر
  created_at: string
  updated_at: string
  rating?: number
  reviews_count?: number
  features?: string[]
  specifications?: Record<string, unknown>
}

export interface MockSchool {
  id: string
  admin_id?: string
  name: string
  name_en?: string
  name_fr?: string
  address?: string
  city?: string
  phone?: string
  email?: string
  website?: string
  logo_url?: string
  graduation_date?: string
  student_count: number
  is_active: boolean
  settings?: Record<string, unknown>
  created_at: string
  updated_at: string
}

export interface MockOrder {
  id: string
  order_number: string
  customer_id: string
  customer_name: string
  customer_email: string
  customer_phone?: string
  status: 'pending' | 'confirmed' | 'in_production' | 'shipped' | 'delivered' | 'cancelled'
  items: MockOrderItem[]
  subtotal: number
  tax: number
  shipping_cost: number
  total: number
  payment_status: 'pending' | 'paid' | 'failed' | 'refunded'
  payment_method?: string
  shipping_address: {
    street: string
    city: string
    state: string
    postal_code: string
    country: string
  }
  tracking_number?: string
  notes?: string
  created_at: string
  updated_at: string
  delivery_date?: string
  school_id?: string
  school_name?: string
}

export interface MockOrderItem {
  id: string
  order_id: string
  product_id: string
  product_name: string
  product_image: string
  category: string
  quantity: number
  unit_price: number
  total_price: number
  customizations?: {
    color?: string
    size?: string
    embroidery?: string
    special_requests?: string
  }
}

// بيانات وهمية للصفحات
export const mockPages: MockPage[] = [
  {
    id: '1',
    slug: 'about-us',
    is_published: true,
    author_id: 'admin-1',
    featured_image: '/images/about-hero.jpg',
    created_at: '2024-01-15T10:00:00Z',
    updated_at: '2024-01-20T14:30:00Z',
    profiles: {
      full_name: 'مدير النظام'
    },
    page_content: [
      {
        id: '1-ar',
        page_id: '1',
        language: 'ar',
        title: 'من نحن',
        content: '<h2>مرحباً بكم في منصة أزياء التخرج</h2><p>نحن منصة متخصصة في تأجير وبيع أزياء التخرج المغربية الأصيلة. نهدف إلى جعل يوم تخرجكم لا يُنسى من خلال توفير أجمل الأزياء التقليدية.</p><p>تأسست منصتنا عام 2024 بهدف خدمة الطلاب والطالبات في جميع أنحاء المغرب، ونفتخر بتقديم خدمات عالية الجودة وأسعار مناسبة.</p>',
        meta_description: 'تعرف على منصة أزياء التخرج المغربية - خدمات تأجير وبيع الأزياء التقليدية',
        meta_keywords: 'أزياء التخرج، المغرب، تأجير، بيع، تقليدي'
      },
      {
        id: '1-en',
        page_id: '1',
        language: 'en',
        title: 'About Us',
        content: '<h2>Welcome to Graduation Attire Platform</h2><p>We are a specialized platform for renting and selling authentic Moroccan graduation attire. We aim to make your graduation day unforgettable by providing the most beautiful traditional outfits.</p><p>Our platform was founded in 2024 to serve students throughout Morocco, and we pride ourselves on providing high-quality services at affordable prices.</p>',
        meta_description: 'Learn about the Moroccan Graduation Attire Platform - traditional outfit rental and sales services',
        meta_keywords: 'graduation attire, Morocco, rental, sales, traditional'
      }
    ]
  },
  {
    id: '2',
    slug: 'services',
    is_published: true,
    author_id: 'admin-1',
    created_at: '2024-01-16T09:00:00Z',
    updated_at: '2024-01-22T16:45:00Z',
    profiles: {
      full_name: 'مدير النظام'
    },
    page_content: [
      {
        id: '2-ar',
        page_id: '2',
        language: 'ar',
        title: 'خدماتنا',
        content: '<h2>خدماتنا المتميزة</h2><h3>تأجير الأزياء</h3><p>نوفر خدمة تأجير أزياء التخرج لفترات مرنة مع ضمان النظافة والجودة.</p><h3>بيع الأزياء</h3><p>إمكانية شراء الأزياء للاحتفاظ بها كذكرى جميلة من يوم التخرج.</p><h3>التخصيص</h3><p>خدمات تخصيص الأزياء حسب المقاسات والتفضيلات الشخصية.</p>',
        meta_description: 'خدمات منصة أزياء التخرج - تأجير وبيع وتخصيص الأزياء التقليدية المغربية',
        meta_keywords: 'خدمات، تأجير، بيع، تخصيص، أزياء التخرج'
      }
    ]
  },
  {
    id: '3',
    slug: 'contact',
    is_published: true,
    author_id: 'admin-1',
    created_at: '2024-01-17T11:00:00Z',
    updated_at: '2024-01-17T11:00:00Z',
    profiles: {
      full_name: 'مدير النظام'
    },
    page_content: [
      {
        id: '3-ar',
        page_id: '3',
        language: 'ar',
        title: 'اتصل بنا',
        content: '<h2>تواصل معنا</h2><p>نحن هنا لخدمتكم في أي وقت. يمكنكم التواصل معنا عبر:</p><ul><li>الهاتف: +212 5XX-XXXXXX</li><li>البريد الإلكتروني: <EMAIL></li><li>العنوان: الدار البيضاء، المغرب</li></ul>',
        meta_description: 'تواصل مع منصة أزياء التخرج المغربية',
        meta_keywords: 'اتصال، تواصل، خدمة العملاء'
      }
    ]
  }
]

// بيانات وهمية للقوائم
export const mockMenuItems: MockMenuItem[] = [
  {
    id: '1',
    title_ar: 'الرئيسية',
    title_en: 'Home',
    title_fr: 'Accueil',
    slug: 'home',
    icon: 'Home',
    order_index: 1,
    is_active: true,
    target_type: 'internal',
    target_value: '/',
    created_at: '2024-01-15T10:00:00Z',
    updated_at: '2024-01-15T10:00:00Z'
  },
  {
    id: '2',
    title_ar: 'من نحن',
    title_en: 'About Us',
    title_fr: 'À propos',
    slug: 'about',
    icon: 'Info',
    order_index: 2,
    is_active: true,
    target_type: 'internal',
    target_value: '/about',
    created_at: '2024-01-15T10:05:00Z',
    updated_at: '2024-01-15T10:05:00Z'
  },
  {
    id: '3',
    title_ar: 'خدماتنا',
    title_en: 'Services',
    title_fr: 'Services',
    slug: 'services',
    icon: 'Settings',
    order_index: 3,
    is_active: true,
    target_type: 'internal',
    target_value: '/services',
    created_at: '2024-01-15T10:10:00Z',
    updated_at: '2024-01-15T10:10:00Z'
  },
  {
    id: '4',
    title_ar: 'المنتجات',
    title_en: 'Products',
    title_fr: 'Produits',
    slug: 'products',
    icon: 'Package',
    order_index: 4,
    is_active: true,
    target_type: 'internal',
    target_value: '/catalog',
    created_at: '2024-01-15T10:15:00Z',
    updated_at: '2024-01-15T10:15:00Z'
  },
  {
    id: '5',
    title_ar: 'تأجير الأزياء',
    title_en: 'Rental',
    title_fr: 'Location',
    slug: 'rental',
    parent_id: '4',
    icon: 'Calendar',
    order_index: 1,
    is_active: true,
    target_type: 'internal',
    target_value: '/catalog?type=rental',
    created_at: '2024-01-15T10:20:00Z',
    updated_at: '2024-01-15T10:20:00Z'
  },
  {
    id: '6',
    title_ar: 'بيع الأزياء',
    title_en: 'Sales',
    title_fr: 'Vente',
    slug: 'sales',
    parent_id: '4',
    icon: 'ShoppingCart',
    order_index: 2,
    is_active: true,
    target_type: 'internal',
    target_value: '/catalog?type=sale',
    created_at: '2024-01-15T10:25:00Z',
    updated_at: '2024-01-15T10:25:00Z'
  },

  {
    id: '8',
    title_ar: 'اتصل بنا',
    title_en: 'Contact',
    title_fr: 'Contact',
    slug: 'contact',
    icon: 'Phone',
    order_index: 6,
    is_active: true,
    target_type: 'internal',
    target_value: '/contact',
    created_at: '2024-01-15T10:35:00Z',
    updated_at: '2024-01-15T10:35:00Z'
  }
]

// بيانات وهمية للفئات
export const mockCategories: MockCategory[] = [
  {
    id: '1',
    name_ar: 'أثواب التخرج',
    name_en: 'Graduation Gowns',
    name_fr: 'Robes de Graduation',
    slug: 'gown',
    icon: '👘',
    description: 'أثواب التخرج الأكاديمية التقليدية',
    is_active: true,
    order_index: 1,
    created_at: '2024-01-15T10:00:00Z',
    updated_at: '2024-01-15T10:00:00Z'
  },
  {
    id: '2',
    name_ar: 'قبعات التخرج',
    name_en: 'Graduation Caps',
    name_fr: 'Chapeaux de Graduation',
    slug: 'cap',
    icon: '🎩',
    description: 'قبعات التخرج الأكاديمية',
    is_active: true,
    order_index: 2,
    created_at: '2024-01-15T10:05:00Z',
    updated_at: '2024-01-15T10:05:00Z'
  },
  {
    id: '3',
    name_ar: 'شرابات التخرج',
    name_en: 'Graduation Tassels',
    name_fr: 'Glands de Graduation',
    slug: 'tassel',
    icon: '🏷️',
    description: 'شرابات التخرج الملونة',
    is_active: true,
    order_index: 3,
    created_at: '2024-01-15T10:10:00Z',
    updated_at: '2024-01-15T10:10:00Z'
  },
  {
    id: '4',
    name_ar: 'أوشحة التخرج',
    name_en: 'Graduation Stoles',
    name_fr: 'Étoles de Graduation',
    slug: 'stole',
    icon: '🧣',
    description: 'أوشحة التخرج المميزة',
    is_active: true,
    order_index: 4,
    created_at: '2024-01-15T10:15:00Z',
    updated_at: '2024-01-15T10:15:00Z'
  },
  {
    id: '5',
    name_ar: 'القلانس الأكاديمية',
    name_en: 'Academic Hoods',
    name_fr: 'Capuches Académiques',
    slug: 'hood',
    icon: '🎓',
    description: 'القلانس الأكاديمية للدرجات العليا',
    is_active: true,
    order_index: 5,
    created_at: '2024-01-15T10:20:00Z',
    updated_at: '2024-01-15T10:20:00Z'
  }
]

// بيانات وهمية للمنتجات
export const mockProducts: MockProduct[] = [
  {
    id: '1',
    name: 'ثوب التخرج الكلاسيكي',
    description: 'ثوب تخرج أنيق مصنوع من أجود الخامات، مناسب لجميع المناسبات الأكاديمية',
    category: 'gown',
    price: 299.99,
    rental_price: 99.99,
    colors: ['أسود', 'أزرق داكن', 'بورجوندي'],
    sizes: ['S', 'M', 'L', 'XL', 'XXL'],
    images: ['/images/products/gown-classic-1.jpg', '/images/products/gown-classic-2.jpg'],
    stock_quantity: 25,
    is_available: true,
    is_published: true,
    created_at: '2024-01-15T10:00:00Z',
    updated_at: '2024-01-20T14:30:00Z',
    rating: 4.8,
    reviews_count: 42,
    features: ['مقاوم للتجاعيد', 'قابل للغسيل', 'خامة عالية الجودة'],
    specifications: {
      material: 'بوليستر عالي الجودة',
      weight: '0.8 كيلو',
      care: 'غسيل جاف أو غسيل عادي'
    }
  },
  {
    id: '2',
    name: 'قبعة التخرج التقليدية',
    description: 'قبعة تخرج تقليدية مع شرابة ذهبية، رمز الإنجاز الأكاديمي',
    category: 'cap',
    price: 79.99,
    rental_price: 29.99,
    colors: ['أسود', 'أزرق داكن'],
    sizes: ['One Size'],
    images: ['/images/products/cap-traditional-1.jpg'],
    stock_quantity: 50,
    is_available: true,
    is_published: true,
    created_at: '2024-01-16T09:00:00Z',
    updated_at: '2024-01-22T16:45:00Z',
    rating: 4.6,
    reviews_count: 28,
    features: ['مقاس واحد يناسب الجميع', 'شرابة ذهبية', 'تصميم تقليدي'],
    specifications: {
      material: 'قطن مخلوط',
      tassel_color: 'ذهبي',
      adjustable: 'نعم'
    }
  },
  {
    id: '3',
    name: 'وشاح التخرج المطرز',
    description: 'وشاح تخرج مطرز بخيوط ذهبية، يضيف لمسة من الأناقة والتميز',
    category: 'stole',
    price: 149.99,
    rental_price: 49.99,
    colors: ['أبيض مع ذهبي', 'أزرق مع فضي', 'أحمر مع ذهبي'],
    sizes: ['One Size'],
    images: ['/images/products/stole-embroidered-1.jpg', '/images/products/stole-embroidered-2.jpg'],
    stock_quantity: 15,
    is_available: true,
    is_published: true,
    created_at: '2024-01-17T11:00:00Z',
    updated_at: '2024-01-25T10:15:00Z',
    rating: 4.9,
    reviews_count: 18,
    features: ['تطريز يدوي', 'خيوط ذهبية', 'تصميم فاخر'],
    specifications: {
      material: 'حرير طبيعي',
      embroidery: 'خيوط ذهبية وفضية',
      length: '150 سم'
    }
  },
  {
    id: '4',
    name: 'شرابة التخرج الذهبية',
    description: 'شرابة تخرج ذهبية اللون، رمز التفوق والإنجاز الأكاديمي',
    category: 'tassel',
    price: 39.99,
    rental_price: 15.99,
    colors: ['ذهبي', 'فضي', 'أزرق', 'أحمر'],
    sizes: ['One Size'],
    images: ['/images/products/tassel-gold-1.jpg'],
    stock_quantity: 100,
    is_available: true,
    is_published: true,
    created_at: '2024-01-18T14:00:00Z',
    updated_at: '2024-01-26T09:30:00Z',
    rating: 4.7,
    reviews_count: 35,
    features: ['خيوط عالية الجودة', 'ألوان ثابتة', 'سهل التركيب'],
    specifications: {
      material: 'خيوط حريرية',
      length: '23 سم',
      attachment: 'مشبك معدني'
    }
  },
  {
    id: '5',
    name: 'قلنسوة الدكتوراه الفاخرة',
    description: 'قلنسوة دكتوراه فاخرة مصممة خصيصاً لحفلات التخرج الأكاديمية العليا',
    category: 'hood',
    price: 199.99,
    rental_price: 79.99,
    colors: ['أسود مع ذهبي', 'أزرق مع فضي'],
    sizes: ['M', 'L', 'XL'],
    images: ['/images/products/hood-doctorate-1.jpg', '/images/products/hood-doctorate-2.jpg'],
    stock_quantity: 8,
    is_available: true,
    is_published: true,
    created_at: '2024-01-19T16:00:00Z',
    updated_at: '2024-01-27T12:00:00Z',
    rating: 5.0,
    reviews_count: 12,
    features: ['تصميم أكاديمي أصيل', 'خامة فاخرة', 'مناسب للدكتوراه'],
    specifications: {
      material: 'مخمل عالي الجودة',
      lining: 'حرير ملون',
      academic_level: 'دكتوراه'
    }
  }
]

// بيانات وهمية للمدارس
export const mockSchools: MockSchool[] = [
  {
    id: '1',
    admin_id: 'admin-school-1',
    name: 'جامعة الإمارات العربية المتحدة',
    name_en: 'United Arab Emirates University',
    name_fr: 'Université des Émirats Arabes Unis',
    address: 'شارع الجامعة، العين',
    city: 'العين',
    phone: '+971-3-713-5000',
    email: '<EMAIL>',
    website: 'https://www.uaeu.ac.ae',
    logo_url: '/images/schools/uaeu-logo.png',
    graduation_date: '2024-06-15',
    student_count: 14500,
    is_active: true,
    settings: {
      graduation_ceremony_location: 'قاعة الاحتفالات الكبرى',
      dress_code: 'formal',
      photography_allowed: true
    },
    created_at: '2024-01-10T08:00:00Z',
    updated_at: '2024-01-25T10:30:00Z'
  },
  {
    id: '2',
    admin_id: 'admin-school-2',
    name: 'الجامعة الأمريكية في الشارقة',
    name_en: 'American University of Sharjah',
    name_fr: 'Université Américaine de Sharjah',
    address: 'شارع الجامعة، الشارقة',
    city: 'الشارقة',
    phone: '+971-6-515-5555',
    email: '<EMAIL>',
    website: 'https://www.aus.edu',
    logo_url: '/images/schools/aus-logo.png',
    graduation_date: '2024-05-20',
    student_count: 6200,
    is_active: true,
    settings: {
      graduation_ceremony_location: 'مسرح الجامعة',
      dress_code: 'academic',
      photography_allowed: true
    },
    created_at: '2024-01-12T09:15:00Z',
    updated_at: '2024-01-28T14:20:00Z'
  },
  {
    id: '3',
    admin_id: 'admin-school-3',
    name: 'جامعة زايد',
    name_en: 'Zayed University',
    name_fr: 'Université Zayed',
    address: 'شارع الشيخ زايد، دبي',
    city: 'دبي',
    phone: '+971-4-402-1111',
    email: '<EMAIL>',
    website: 'https://www.zu.ac.ae',
    logo_url: '/images/schools/zu-logo.png',
    graduation_date: '2024-06-10',
    student_count: 9800,
    is_active: true,
    settings: {
      graduation_ceremony_location: 'مركز المؤتمرات',
      dress_code: 'formal',
      photography_allowed: false
    },
    created_at: '2024-01-15T11:00:00Z',
    updated_at: '2024-02-01T16:45:00Z'
  },
  {
    id: '4',
    admin_id: 'admin-school-4',
    name: 'كلية الإمارات للتكنولوجيا',
    name_en: 'Emirates Institute of Technology',
    name_fr: 'Institut de Technologie des Émirats',
    address: 'المنطقة الأكاديمية، أبوظبي',
    city: 'أبوظبي',
    phone: '+971-2-401-4000',
    email: '<EMAIL>',
    website: 'https://www.eit.ac.ae',
    logo_url: '/images/schools/eit-logo.png',
    graduation_date: '2024-07-05',
    student_count: 3500,
    is_active: true,
    settings: {
      graduation_ceremony_location: 'القاعة الرئيسية',
      dress_code: 'business',
      photography_allowed: true
    },
    created_at: '2024-01-18T13:30:00Z',
    updated_at: '2024-02-05T09:15:00Z'
  },
  {
    id: '5',
    admin_id: 'admin-school-5',
    name: 'معهد أبوظبي للتعليم التقني',
    name_en: 'Abu Dhabi Technical Institute',
    name_fr: 'Institut Technique d\'Abu Dhabi',
    address: 'المنطقة الصناعية، أبوظبي',
    city: 'أبوظبي',
    phone: '+971-2-505-2000',
    email: '<EMAIL>',
    website: 'https://www.adti.ac.ae',
    graduation_date: '2024-06-25',
    student_count: 2800,
    is_active: false,
    settings: {
      graduation_ceremony_location: 'مركز التدريب',
      dress_code: 'casual',
      photography_allowed: true
    },
    created_at: '2024-01-20T15:45:00Z',
    updated_at: '2024-02-10T12:00:00Z'
  }
]

// بيانات وهمية للطلبات
export const mockOrders: MockOrder[] = [
  {
    id: '1',
    order_number: 'GT-240120-001',
    customer_id: 'student-1',
    customer_name: 'أحمد محمد علي',
    customer_email: '<EMAIL>',
    customer_phone: '+971-50-123-4567',
    status: 'in_production',
    items: [
      {
        id: '1',
        order_id: '1',
        product_id: '1',
        product_name: 'ثوب التخرج الكلاسيكي',
        product_image: '/images/products/gown-classic-1.jpg',
        category: 'gown',
        quantity: 1,
        unit_price: 299.99,
        total_price: 299.99,
        customizations: {
          color: 'أسود',
          size: 'L',
          embroidery: 'أحمد علي - بكالوريوس هندسة'
        }
      },
      {
        id: '2',
        order_id: '1',
        product_id: '2',
        product_name: 'قبعة التخرج الأكاديمية',
        product_image: '/images/products/cap-academic-1.jpg',
        category: 'cap',
        quantity: 1,
        unit_price: 89.99,
        total_price: 89.99,
        customizations: {
          color: 'أسود',
          size: 'M'
        }
      }
    ],
    subtotal: 389.98,
    tax: 19.50,
    shipping_cost: 25.00,
    total: 434.48,
    payment_status: 'paid',
    payment_method: 'credit_card',
    shipping_address: {
      street: 'شارع الجامعة، مبنى 12، شقة 304',
      city: 'العين',
      state: 'أبوظبي',
      postal_code: '17666',
      country: 'الإمارات العربية المتحدة'
    },
    tracking_number: 'TRK-GT-001-2024',
    notes: 'يرجى التسليم قبل حفل التخرج',
    created_at: '2024-01-20T10:30:00Z',
    updated_at: '2024-01-22T14:15:00Z',
    delivery_date: '2024-02-15T00:00:00Z',
    school_id: '1',
    school_name: 'جامعة الإمارات العربية المتحدة'
  },
  {
    id: '2',
    order_number: 'GT-**********',
    customer_id: 'student-2',
    customer_name: 'فاطمة سالم الزهراني',
    customer_email: '<EMAIL>',
    customer_phone: '+971-56-789-0123',
    status: 'delivered',
    items: [
      {
        id: '3',
        order_id: '2',
        product_id: '3',
        product_name: 'ثوب التخرج المميز',
        product_image: '/images/products/gown-premium-1.jpg',
        category: 'gown',
        quantity: 1,
        unit_price: 399.99,
        total_price: 399.99,
        customizations: {
          color: 'أزرق داكن',
          size: 'M',
          embroidery: 'فاطمة الزهراني - ماجستير إدارة أعمال'
        }
      }
    ],
    subtotal: 399.99,
    tax: 20.00,
    shipping_cost: 30.00,
    total: 449.99,
    payment_status: 'paid',
    payment_method: 'bank_transfer',
    shipping_address: {
      street: 'شارع الكورنيش، برج الإمارات، الطابق 15',
      city: 'الشارقة',
      state: 'الشارقة',
      postal_code: '27272',
      country: 'الإمارات العربية المتحدة'
    },
    tracking_number: 'TRK-GT-002-2024',
    created_at: '2024-01-21T09:15:00Z',
    updated_at: '2024-01-25T16:30:00Z',
    delivery_date: '2024-01-28T00:00:00Z',
    school_id: '2',
    school_name: 'الجامعة الأمريكية في الشارقة'
  },
  {
    id: '3',
    order_number: 'GT-**********',
    customer_id: 'student-3',
    customer_name: 'خالد عبدالله المنصوري',
    customer_email: '<EMAIL>',
    customer_phone: '+971-52-456-7890',
    status: 'pending',
    items: [
      {
        id: '4',
        order_id: '3',
        product_id: '1',
        product_name: 'ثوب التخرج الكلاسيكي',
        product_image: '/images/products/gown-classic-1.jpg',
        category: 'gown',
        quantity: 1,
        unit_price: 299.99,
        total_price: 299.99,
        customizations: {
          color: 'بورجوندي',
          size: 'XL'
        }
      },
      {
        id: '5',
        order_id: '3',
        product_id: '4',
        product_name: 'وشاح التخرج المطرز',
        product_image: '/images/products/stole-embroidered-1.jpg',
        category: 'stole',
        quantity: 1,
        unit_price: 149.99,
        total_price: 149.99,
        customizations: {
          color: 'ذهبي',
          embroidery: 'كلية الهندسة'
        }
      }
    ],
    subtotal: 449.98,
    tax: 22.50,
    shipping_cost: 25.00,
    total: 497.48,
    payment_status: 'pending',
    shipping_address: {
      street: 'شارع الشيخ زايد، مجمع دبي الأكاديمي',
      city: 'دبي',
      state: 'دبي',
      postal_code: '391186',
      country: 'الإمارات العربية المتحدة'
    },
    created_at: '2024-01-22T14:45:00Z',
    updated_at: '2024-01-22T14:45:00Z',
    school_id: '3',
    school_name: 'جامعة زايد'
  }
]

// مساعدات للتعامل مع البيانات الوهمية
export class MockDataManager {
  private static getStorageKey(type: 'pages' | 'menuItems' | 'products' | 'categories' | 'schools' | 'orders' | 'aiProviders'): string {
    return `mockData_${type}`
  }

  static getPages(): MockPage[] {
    if (typeof window === 'undefined') return mockPages

    const stored = localStorage.getItem(this.getStorageKey('pages'))
    return stored ? JSON.parse(stored) : mockPages
  }

  static getMenuItems(): MockMenuItem[] {
    if (typeof window === 'undefined') return mockMenuItems

    const stored = localStorage.getItem(this.getStorageKey('menuItems'))
    return stored ? JSON.parse(stored) : mockMenuItems
  }

  static getProducts(): MockProduct[] {
    if (typeof window === 'undefined') {
      console.log('Server side - returning mock products:', mockProducts.length)
      return mockProducts
    }

    const stored = localStorage.getItem(this.getStorageKey('products'))
    const products = stored ? JSON.parse(stored) : mockProducts
    console.log('Client side - loaded products:', products.length)
    console.log('Product IDs:', products.map((p: MockProduct) => p.id))
    return products
  }

  static getCategories(): MockCategory[] {
    if (typeof window === 'undefined') return mockCategories

    const stored = localStorage.getItem(this.getStorageKey('categories'))
    return stored ? JSON.parse(stored) : mockCategories
  }

  static getSchools(): MockSchool[] {
    if (typeof window === 'undefined') return mockSchools

    const stored = localStorage.getItem(this.getStorageKey('schools'))
    return stored ? JSON.parse(stored) : mockSchools
  }

  static getOrders(): MockOrder[] {
    if (typeof window === 'undefined') return mockOrders

    const stored = localStorage.getItem(this.getStorageKey('orders'))
    return stored ? JSON.parse(stored) : mockOrders
  }

  static savePages(pages: MockPage[]): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem(this.getStorageKey('pages'), JSON.stringify(pages))
    }
  }

  static saveMenuItems(items: MockMenuItem[]): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem(this.getStorageKey('menuItems'), JSON.stringify(items))
    }
  }

  static saveProducts(products: MockProduct[]): void {
    if (typeof window !== 'undefined') {
      console.log('Saving products to localStorage:', products.length)
      localStorage.setItem(this.getStorageKey('products'), JSON.stringify(products))

      // التحقق من أن البيانات حُفظت بشكل صحيح
      const saved = localStorage.getItem(this.getStorageKey('products'))
      const parsedSaved = saved ? JSON.parse(saved) : []
      console.log('Verified saved products count:', parsedSaved.length)
    }
  }

  static saveCategories(categories: MockCategory[]): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem(this.getStorageKey('categories'), JSON.stringify(categories))
    }
  }

  static saveSchools(schools: MockSchool[]): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem(this.getStorageKey('schools'), JSON.stringify(schools))
    }
  }

  static saveOrders(orders: MockOrder[]): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem(this.getStorageKey('orders'), JSON.stringify(orders))
    }
  }

  static generateId(): string {
    return Date.now().toString() + Math.random().toString(36).substring(2, 11)
  }

  // مسح جميع البيانات المحفوظة (للاختبار)
  static clearAllData(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('mockAIModels')
      localStorage.removeItem('mockModelActivities')
      localStorage.removeItem('mockPages')
      localStorage.removeItem('mockPageTemplates')
      localStorage.removeItem('mockPageProjects')
      localStorage.removeItem('mockComponentLibrary')
    }
  }

  static generateOrderNumber(): string {
    const date = new Date()
    const year = date.getFullYear().toString().slice(-2)
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const orders = this.getOrders()
    const todayOrders = orders.filter(order =>
      order.created_at.startsWith(`${date.getFullYear()}-${month}-${day}`)
    )
    const orderCount = (todayOrders.length + 1).toString().padStart(3, '0')
    return `GT-${year}${month}${day}-${orderCount}`
  }

  // إدارة نماذج الذكاء الاصطناعي
  static getAIModels(): AIModel[] {
    if (typeof window === 'undefined') return this.defaultAIModels

    const stored = localStorage.getItem('mockAIModels')
    if (stored) {
      return JSON.parse(stored)
    }
    return this.defaultAIModels
  }

  static saveAIModels(models: AIModel[]): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem('mockAIModels', JSON.stringify(models))
    }
  }

  static getModelActivities(): ModelActivity[] {
    if (typeof window === 'undefined') return this.defaultModelActivities

    const stored = localStorage.getItem('mockModelActivities')
    if (stored) {
      return JSON.parse(stored)
    }
    return this.defaultModelActivities
  }

  static saveModelActivities(activities: ModelActivity[]): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem('mockModelActivities', JSON.stringify(activities))
    }
  }

  // إدارة قوالب الصفحات
  static getPageTemplates(): PageTemplate[] {
    if (typeof window === 'undefined') return this.defaultPageTemplates

    const stored = localStorage.getItem('mockPageTemplates')
    if (stored) {
      return JSON.parse(stored)
    }
    return this.defaultPageTemplates
  }

  static savePageTemplates(templates: PageTemplate[]): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem('mockPageTemplates', JSON.stringify(templates))
    }
  }

  static getPageProjects(): PageProject[] {
    if (typeof window === 'undefined') return this.defaultPageProjects

    const stored = localStorage.getItem('mockPageProjects')
    if (stored) {
      return JSON.parse(stored)
    }
    return this.defaultPageProjects
  }

  static savePageProjects(projects: PageProject[]): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem('mockPageProjects', JSON.stringify(projects))
    }
  }

  static getComponentLibrary(): ComponentLibraryItem[] {
    if (typeof window === 'undefined') return this.defaultComponentLibrary

    const stored = localStorage.getItem('mockComponentLibrary')
    if (stored) {
      return JSON.parse(stored)
    }
    return this.defaultComponentLibrary
  }

  static saveComponentLibrary(components: ComponentLibraryItem[]): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem('mockComponentLibrary', JSON.stringify(components))
    }
  }

  // البيانات الافتراضية لنماذج الذكاء الاصطناعي (فارغة للبداية)
  static defaultAIModels: AIModel[] = []

  static defaultModelActivities: ModelActivity[] = []

  // البيانات الافتراضية لقوالب الصفحات
  static defaultPageTemplates: PageTemplate[] = [
    {
      id: 'template-landing-1',
      name: 'Landing Page - Modern',
      nameAr: 'صفحة هبوط - عصرية',
      nameEn: 'Landing Page - Modern',
      nameFr: 'Page d\'atterrissage - Moderne',
      description: 'قالب صفحة هبوط عصرية مع تصميم جذاب',
      category: 'landing',
      components: [
        {
          id: 'hero-1',
          type: 'hero',
          name: 'Hero Section',
          props: {
            content: 'مرحباً بكم في منصة أزياء التخرج',
            style: { backgroundColor: '#1F2937', color: '#FFFFFF' }
          },
          position: { x: 0, y: 0 },
          size: { width: '100%', height: '500px' },
          isVisible: true
        }
      ],
      preview: '/images/templates/landing-modern.jpg',
      thumbnail: '/images/templates/landing-modern-thumb.jpg',
      isAIGenerated: false,
      isPremium: false,
      tags: ['landing', 'modern', 'business'],
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
      usageCount: 45,
      rating: 4.8,
      metadata: {
        colors: ['#1F2937', '#FFFFFF', '#3B82F6'],
        fonts: ['Inter', 'Cairo'],
        layout: 'single-page',
        responsive: true
      }
    }
  ]

  // البيانات الافتراضية لمشاريع الصفحات
  static defaultPageProjects: PageProject[] = [
    {
      id: 'project-1',
      name: 'موقع أزياء التخرج الرئيسي',
      description: 'الموقع الرئيسي لعرض منتجات أزياء التخرج',
      components: [],
      templateId: 'template-landing-1',
      generationMode: 'template',
      settings: {
        title: 'أزياء التخرج - منصة مغربية متخصصة',
        description: 'أول منصة مغربية لتأجير وبيع أزياء التخرج',
        keywords: ['أزياء التخرج', 'تأجير', 'المغرب'],
        language: 'ar',
        direction: 'rtl'
      },
      isPublished: false,
      createdAt: '2024-01-15T00:00:00Z',
      updatedAt: '2024-01-20T10:30:00Z',
      createdBy: 'admin-1',
      version: 1
    }
  ]

  // البيانات الافتراضية لمكتبة المكونات
  static defaultComponentLibrary: ComponentLibraryItem[] = [
    {
      id: 'comp-hero',
      name: 'Hero Section',
      nameAr: 'قسم البطل',
      type: 'hero',
      category: 'layout',
      description: 'قسم رئيسي جذاب في أعلى الصفحة',
      icon: 'Layout',
      preview: '/images/components/hero-preview.jpg',
      defaultProps: {
        content: 'عنوان رئيسي جذاب',
        style: { backgroundColor: '#1F2937', color: '#FFFFFF' }
      },
      defaultSize: { width: '100%', height: '500px' },
      isCustom: false,
      isPremium: false,
      tags: ['layout', 'header', 'hero'],
      usageCount: 156
    },
    {
      id: 'comp-button',
      name: 'Button',
      nameAr: 'زر',
      type: 'button',
      category: 'interactive',
      description: 'زر تفاعلي قابل للتخصيص',
      icon: 'MousePointer',
      preview: '/images/components/button-preview.jpg',
      defaultProps: {
        content: 'انقر هنا',
        style: { backgroundColor: '#3B82F6', color: '#FFFFFF' }
      },
      defaultSize: { width: '120px', height: '40px' },
      isCustom: false,
      isPremium: false,
      tags: ['interactive', 'button', 'cta'],
      usageCount: 234
    }
  ]

  // إدارة مزودي الذكاء الاصطناعي
  static getAIProviders(): MockAIProvider[] {
    if (typeof window === 'undefined') return []

    const stored = localStorage.getItem(this.getStorageKey('aiProviders'))
    return stored ? JSON.parse(stored) : []
  }

  static addAIProvider(provider: MockAIProvider): MockAIProvider {
    if (typeof window === 'undefined') return provider

    const providers = this.getAIProviders()
    providers.push(provider)
    localStorage.setItem(this.getStorageKey('aiProviders'), JSON.stringify(providers))
    return provider
  }

  static updateAIProvider(id: string, updatedProvider: Partial<MockAIProvider>): MockAIProvider | null {
    if (typeof window === 'undefined') return null

    const providers = this.getAIProviders()
    const index = providers.findIndex(p => p.id === id)

    if (index === -1) return null

    providers[index] = { ...providers[index], ...updatedProvider }
    localStorage.setItem(this.getStorageKey('aiProviders'), JSON.stringify(providers))
    return providers[index]
  }

  static deleteAIProvider(id: string): boolean {
    if (typeof window === 'undefined') return false

    const providers = this.getAIProviders()
    const filteredProviders = providers.filter(p => p.id !== id)

    if (filteredProviders.length === providers.length) return false

    localStorage.setItem(this.getStorageKey('aiProviders'), JSON.stringify(filteredProviders))
    return true
  }

  static getAIProviderById(id: string): MockAIProvider | null {
    if (typeof window === 'undefined') return null

    const providers = this.getAIProviders()
    return providers.find(p => p.id === id) || null
  }
}

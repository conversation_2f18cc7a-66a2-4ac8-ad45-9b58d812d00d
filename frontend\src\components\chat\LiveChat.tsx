
"use client"

import {
  useState,
  useEffect,
  useRef
} from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  MessageCircle,
  Send,
  X,
  Maximize2,
  Minimize2,
  Clock,
  Check,
  CheckCheck,
  Paperclip,
  Smile
} from 'lucide-react'

// أنواع البيانات
interface ChatMessage {
  id: string
  content: string
  isFromUser: boolean
  timestamp: string
  status?: 'sending' | 'sent' | 'delivered' | 'read'
  attachments?: string[]
}

interface ChatAgent {
  id: string
  name: string
  avatar: string
  status: 'online' | 'away' | 'offline'
  isTyping: boolean
}

export function LiveChat() {
  const { profile } = useAuth()
  const [isOpen, setIsOpen] = useState(false)
  const [isMinimized, setIsMinimized] = useState(false)
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [newMessage, setNewMessage] = useState('')
  const [isConnected, setIsConnected] = useState(false)
  const [currentAgent, setCurrentAgent] = useState<ChatAgent | null>(null)
  const [isTyping, setIsTyping] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // محاكاة الاتصال بالدردشة
  useEffect(() => {
    if (isOpen && !isConnected) {
      // محاكاة الاتصال
      setTimeout(() => {
        setIsConnected(true)
        setCurrentAgent({
          id: 'agent-1',
          name: 'سارة أحمد',
          avatar: '/api/placeholder/40/40',
          status: 'online',
          isTyping: false
        })
        
        // رسالة ترحيب
        addMessage({
          content: `مرحباً ${profile?.full_name || 'بك'}! أنا سارة من فريق الدعم الفني. كيف يمكنني مساعدتك اليوم؟`,
          isFromUser: false
        })
      }, 1500)
    }
  }, [isOpen, isConnected, profile])

  // التمرير التلقائي للأسفل
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  const addMessage = (messageData: Omit<ChatMessage, 'id' | 'timestamp' | 'status'>) => {
    const newMsg: ChatMessage = {
      ...messageData,
      id: Date.now().toString(),
      timestamp: new Date().toISOString(),
      status: messageData.isFromUser ? 'sending' : 'delivered'
    }
    
    setMessages(prev => [...prev, newMsg])
    
    // محاكاة تحديث حالة الرسالة
    if (messageData.isFromUser) {
      setTimeout(() => {
        setMessages(prev => 
          prev.map(msg => 
            msg.id === newMsg.id ? { ...msg, status: 'delivered' } : msg
          )
        )
      }, 1000)
    }
  }

  const handleSendMessage = () => {
    if (!newMessage.trim()) return

    addMessage({
      content: newMessage,
      isFromUser: true
    })

    setNewMessage('')
    
    // محاكاة رد الوكيل
    setIsTyping(true)
    setTimeout(() => {
      setIsTyping(false)
      
      // ردود تلقائية بسيطة
      const autoReplies = [
        'شكراً لك على تواصلك معنا. سأقوم بالتحقق من هذا الأمر.',
        'فهمت طلبك. دعني أساعدك في حل هذه المشكلة.',
        'هذا سؤال ممتاز. سأحتاج لبعض التفاصيل الإضافية.',
        'سأقوم بتحويل طلبك للقسم المختص وسنتواصل معك قريباً.',
        'هل يمكنك تزويدي برقم الطلب لأتمكن من مساعدتك بشكل أفضل؟'
      ]
      
      const randomReply = autoReplies[Math.floor(Math.random() * autoReplies.length)]
      addMessage({
        content: randomReply,
        isFromUser: false
      })
    }, 2000)
  }

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case 'sending': return <Clock className="h-3 w-3 text-gray-400" />
      case 'sent': return <Check className="h-3 w-3 text-gray-400" />
      case 'delivered': return <CheckCheck className="h-3 w-3 text-blue-500" />
      case 'read': return <CheckCheck className="h-3 w-3 text-green-500" />
      default: return null
    }
  }

  if (!isOpen) {
    return (
      <Button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-6 right-6 z-50 rounded-full w-14 h-14 shadow-lg"
        size="lg"
      >
        <MessageCircle className="h-6 w-6" />
      </Button>
    )
  }

  return (
    <Card className={`fixed bottom-6 right-6 z-50 shadow-xl transition-all duration-300 ${
      isMinimized ? 'w-80 h-16' : 'w-80 h-96'
    }`}>
      {/* Header */}
      <CardHeader className="p-4 bg-blue-600 text-white rounded-t-lg">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="relative">
              <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center">
                <MessageCircle className="h-4 w-4 text-blue-600" />
              </div>
              {isConnected && (
                <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
              )}
            </div>
            <div>
              <h3 className="font-medium text-sm arabic-text">
                {isConnected ? 'الدردشة المباشرة' : 'جاري الاتصال...'}
              </h3>
              {currentAgent && (
                <p className="text-xs opacity-90 arabic-text">
                  {currentAgent.name} - {currentAgent.status === 'online' ? 'متاح' : 'غير متاح'}
                </p>
              )}
            </div>
          </div>
          
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMinimized(!isMinimized)}
              className="h-8 w-8 p-0 text-white hover:bg-white/20"
            >
              {isMinimized ? <Maximize2 className="h-4 w-4" /> : <Minimize2 className="h-4 w-4" />}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsOpen(false)}
              className="h-8 w-8 p-0 text-white hover:bg-white/20"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      {/* Chat Content */}
      {!isMinimized && (
        <CardContent className="p-0 flex flex-col h-80">
          {/* Messages */}
          <ScrollArea className="flex-1 p-4">
            {!isConnected ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                  <p className="text-sm text-gray-500 arabic-text">جاري الاتصال بفريق الدعم...</p>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.isFromUser ? 'justify-end' : 'justify-start'}`}
                  >
                    <div className={`max-w-[80%] ${
                      message.isFromUser ? 'bg-blue-600 text-white rounded-l-lg rounded-tr-lg'
                        : 'bg-gray-100 dark:bg-gray-800 rounded-r-lg rounded-tl-lg'
                    } p-3`}>
                      <p className="text-sm arabic-text">{message.content}</p>
                      <div className="flex items-center justify-between mt-1">
                        <span className="text-xs opacity-70">
                          {new Date(message.timestamp).toLocaleTimeString('ar-SA', {
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </span>
                        {message.isFromUser && getStatusIcon(message.status)}
                      </div>
                    </div>
                  </div>
                ))}
                
                {/* Typing Indicator */}
                {isTyping && (
                  <div className="flex justify-start">
                    <div className="bg-gray-100 dark:bg-gray-800 rounded-r-lg rounded-tl-lg p-3">
                      <div className="flex items-center gap-1">
                        <div className="flex gap-1">
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                        </div>
                        <span className="text-xs text-gray-500 mr-2 arabic-text">يكتب...</span>
                      </div>
                    </div>
                  </div>
                )}
                
                <div ref={messagesEndRef} />
              </div>
            )}
          </ScrollArea>

          {/* Input */}
          {isConnected && (
            <div className="p-4 border-t">
              <div className="flex items-center gap-2">
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <Paperclip className="h-4 w-4" />
                </Button>
                <Input
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  placeholder="اكتب رسالتك..."
                  className="flex-1 arabic-text"
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      handleSendMessage()
                    }
                  }}
                />
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <Smile className="h-4 w-4" />
                </Button>
                <Button
                  onClick={handleSendMessage}
                  disabled={!newMessage.trim()}
                  size="sm"
                >
                  <Send className="h-4 w-4" />
                </Button>
              </div>
              
              <div className="flex items-center justify-between mt-2 text-xs text-gray-500">
                <span className="arabic-text">اضغط Enter للإرسال</span>
                {currentAgent && (
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="arabic-text">{currentAgent.name} متاح</span>
                  </div>
                )}
              </div>
            </div>
          )}
        </CardContent>
      )}
    </Card>
  )
}

// مكون مبسط لأيقونة الدردشة
export function ChatButton() {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <>
      <Button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-6 left-6 z-40 rounded-full w-12 h-12 shadow-lg bg-green-600 hover:bg-green-700"
        size="sm"
      >
        <MessageCircle className="h-5 w-5" />
      </Button>
      
      {isOpen && <LiveChat />}
    </>
  )
}

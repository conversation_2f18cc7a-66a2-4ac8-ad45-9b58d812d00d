{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\n\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\n\nimport { cn } from \"@/lib/utils\"\nimport { Check, ChevronRight, Circle } from \"lucide-react\"\n\nconst CheckIcon = Check\nconst ChevronRightIcon = ChevronRight\nconst CircleIcon = Circle\n\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAIA;AAEA;AACA;AAAA;AAAA;AANA;;;;;AAQA,MAAM,YAAY,uMAAA,CAAA,QAAK;AACvB,MAAM,mBAAmB,yNAAA,CAAA,eAAY;AACrC,MAAM,aAAa,yMAAA,CAAA,SAAM;AAGzB,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,6LAAC,+KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MAlBS;AAoBT,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,6LAAC,+KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,6LAAC,+KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC;wBAAU,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;MAxBS;AA0BT,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC;wBAAW,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;MAtBS;AAwBT,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAlBS;AAoBT,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;OAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS;AAgBT,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,6LAAC,+KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;OAJS;AAMT,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC;gBAAiB,WAAU;;;;;;;;;;;;AAGlC;OAtBS;AAwBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS", "debugId": null}}, {"offset": {"line": 306, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/theme-toggle.tsx"], "sourcesContent": ["\n\"use client\"\n\nimport * as React from \"react\"\nimport { useTheme } from \"next-themes\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\"\nimport { Sun, Moon } from \"lucide-react\"\n\nexport function ThemeToggle() {\n  const { setTheme } = useTheme()\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"outline\" size=\"icon\">\n          <Sun className=\"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\n          <Moon className=\"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\n          <span className=\"sr-only\">Toggle theme</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent align=\"end\">\n        <DropdownMenuItem onClick={() => setTheme(\"light\")}>\n          Light\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme(\"dark\")}>\n          Dark\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme(\"system\")}>\n          System\n        </DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;AAIA;AACA;AACA;AAMA;AAAA;;;AAXA;;;;;AAaO,SAAS;;IACd,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAE5B,qBACE,6LAAC,+IAAA,CAAA,eAAY;;0BACX,6LAAC,+IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,MAAK;;sCAC7B,6LAAC,mMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;sCACf,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,6LAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,6LAAC,+IAAA,CAAA,sBAAmB;gBAAC,OAAM;;kCACzB,6LAAC,+IAA<PERSON>,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAU;;;;;;kCAGpD,6LAAC,+IAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAS;;;;;;kCAGnD,6LAAC,+IAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAW;;;;;;;;;;;;;;;;;;AAM7D;GAzBgB;;QACO,mJAAA,CAAA,WAAQ;;;KADf", "debugId": null}}, {"offset": {"line": 423, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/lib/i18n.ts"], "sourcesContent": ["\nexport type Locale = 'ar' | 'fr' | 'en';\n\nexport const locales: Locale[] = ['ar', 'fr', 'en'];\n\nexport const defaultLocale: Locale = 'ar';\n\nexport const localeNames = {\n  ar: 'العربية',\n  fr: 'Français', \n  en: 'English'\n};\n\nexport const localeFlags = {\n  ar: '🇲🇦',\n  fr: '🇫🇷',\n  en: '🇬🇧'\n};\n\nexport const rtlLocales: Locale[] = ['ar'];\n\nexport function isRtlLocale(locale: Locale): boolean {\n  return rtlLocales.includes(locale);\n}\n"], "names": [], "mappings": ";;;;;;;;AAGO,MAAM,UAAoB;IAAC;IAAM;IAAM;CAAK;AAE5C,MAAM,gBAAwB;AAE9B,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEO,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEO,MAAM,aAAuB;IAAC;CAAK;AAEnC,SAAS,YAAY,MAAc;IACxC,OAAO,WAAW,QAAQ,CAAC;AAC7B", "debugId": null}}, {"offset": {"line": 477, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/hooks/useTranslation.ts"], "sourcesContent": ["\n\"use client\"\n\nimport { useState, useEffect } from 'react';\nimport { Locale, defaultLocale } from '@/lib/i18n';\n\n// Import translation files\nimport arTranslations from '@/locales/ar.json';\nimport frTranslations from '@/locales/fr.json';\nimport enTranslations from '@/locales/en.json';\n\nconst translations = {\n  ar: arTranslations,\n  fr: frTranslations,\n  en: enTranslations,\n};\n\nexport function useTranslation() {\n  const [locale, setLocale] = useState<Locale>(defaultLocale);\n\n  useEffect(() => {\n    // Get locale from localStorage or use default\n    const savedLocale = localStorage.getItem('locale') as Locale;\n    if (savedLocale && ['ar', 'fr', 'en'].includes(savedLocale)) {\n      setLocale(savedLocale);\n    }\n  }, []);\n\n  const changeLocale = (newLocale: Locale) => {\n    setLocale(newLocale);\n    localStorage.setItem('locale', newLocale);\n    \n    // Update document direction and language\n    document.documentElement.lang = newLocale;\n    document.documentElement.dir = newLocale === 'ar' ? 'rtl' : 'ltr';\n  };\n\n  const t = (key: string): string => {\n    const keys = key.split('.');\n    let value: unknown = translations[locale];\n    \n    for (const k of keys) {\n      value = value?.[k];\n    }\n    \n    return value || key;\n  };\n\n  return {\n    locale,\n    changeLocale,\n    t,\n  };\n}\n"], "names": [], "mappings": ";;;AAGA;AACA;AAEA,2BAA2B;AAC3B;AACA;AACA;;AARA;;;;;;AAUA,MAAM,eAAe;IACnB,IAAI,4FAAA,CAAA,UAAc;IAClB,IAAI,4FAAA,CAAA,UAAc;IAClB,IAAI,4FAAA,CAAA,UAAc;AACpB;AAEO,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,qHAAA,CAAA,gBAAa;IAE1D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,8CAA8C;YAC9C,MAAM,cAAc,aAAa,OAAO,CAAC;YACzC,IAAI,eAAe;gBAAC;gBAAM;gBAAM;aAAK,CAAC,QAAQ,CAAC,cAAc;gBAC3D,UAAU;YACZ;QACF;mCAAG,EAAE;IAEL,MAAM,eAAe,CAAC;QACpB,UAAU;QACV,aAAa,OAAO,CAAC,UAAU;QAE/B,yCAAyC;QACzC,SAAS,eAAe,CAAC,IAAI,GAAG;QAChC,SAAS,eAAe,CAAC,GAAG,GAAG,cAAc,OAAO,QAAQ;IAC9D;IAEA,MAAM,IAAI,CAAC;QACT,MAAM,OAAO,IAAI,KAAK,CAAC;QACvB,IAAI,QAAiB,YAAY,CAAC,OAAO;QAEzC,KAAK,MAAM,KAAK,KAAM;YACpB,QAAQ,OAAO,CAAC,EAAE;QACpB;QAEA,OAAO,SAAS;IAClB;IAEA,OAAO;QACL;QACA;QACA;IACF;AACF;GApCgB", "debugId": null}}, {"offset": {"line": 545, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/language-toggle.tsx"], "sourcesContent": ["\n\"use client\"\n\nimport * as React from \"react\"\n\nimport { useTranslation } from \"@/hooks/useTranslation\"\nimport { Locale, localeNames, localeFlags } from \"@/lib/i18n\"\n\nimport { Button } from \"@/components/ui/button\"\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\"\nimport {\n  Globe,\n  ChevronDown\n} from 'lucide-react'\n\nexport function LanguageToggle() {\n  const { locale, changeLocale } = useTranslation()\n\n  const getCurrentLanguage = () => {\n    return {\n      flag: localeFlags[locale],\n      name: localeNames[locale],\n      code: locale.toUpperCase()\n    }\n  }\n\n  const currentLang = getCurrentLanguage ()\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          className=\"h-9 px-3 gap-2 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-300 group\"\n        >\n          <div className=\"flex items-center gap-2\">\n            <span className=\"text-lg group-hover:scale-110 transition-transform duration-300\">\n              {currentLang.flag}\n            </span>\n            <span className=\"hidden sm:inline-block text-sm font-medium\">\n              {currentLang.code}\n            </span>\n          </div>\n          <div className=\"h-4 w-4 opacity-60 group-hover:opacity-100 transition-opacity duration-300\" />\n          <span className=\"sr-only\">تغيير اللغة / Change language</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent\n        align=\"end\"\n        className=\"w-48 p-2\"\n        sideOffset={8}\n      >\n        <DropdownMenu className=\"text-xs font-medium text-gray-500 dark:text-gray-400 px-2 py-1\">\n          {locale === 'ar' ? 'اختر اللغة' : locale === 'fr' ? 'Choisir la langue' : 'Choose Language'}\n        </DropdownMenu >\n        <DropdownMenu />\n        {Object.entries(localeNames).map(([code, name]) => (\n          <DropdownMenuItem\n            key={code}\n            onClick={() => changeLocale(code as Locale)}\n            className={`flex items-center gap-3 px-3 py-2.5 rounded-lg cursor-pointer transition-all duration-200 ${\n              locale === code\n                ? \"bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300\"\n                : \"hover:bg-gray-100 dark:hover:bg-gray-700\"\n            }`}\n          >\n            <span className=\"text-lg\">{localeFlags[code as Locale]}</span>\n            <div className=\"flex-1\">\n              <div className=\"font-medium text-sm\">{name}</div>\n              <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n                {code === 'ar' ? 'العربية' : code === 'fr' ? 'Français' : 'English'}\n              </div>\n            </div>\n            {locale === code && (\n              <div className=\"h-4 w-4 text-blue-600 dark:text-blue-400\" />\n            )}\n          </DropdownMenuItem>\n        ))}\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;AAKA;AACA;AAEA;AACA;;;AARA;;;;;AAmBO,SAAS;;IACd,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAE9C,MAAM,qBAAqB;QACzB,OAAO;YACL,MAAM,qHAAA,CAAA,cAAW,CAAC,OAAO;YACzB,MAAM,qHAAA,CAAA,cAAW,CAAC,OAAO;YACzB,MAAM,OAAO,WAAW;QAC1B;IACF;IAEA,MAAM,cAAc;IAEpB,qBACE,6LAAC,+IAAA,CAAA,eAAY;;0BACX,6LAAC,+IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CACb,YAAY,IAAI;;;;;;8CAEnB,6LAAC;oCAAK,WAAU;8CACb,YAAY,IAAI;;;;;;;;;;;;sCAGrB,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,6LAAC,+IAAA,CAAA,sBAAmB;gBAClB,OAAM;gBACN,WAAU;gBACV,YAAY;;kCAEZ,6LAAC,+IAAA,CAAA,eAAY;wBAAC,WAAU;kCACrB,WAAW,OAAO,eAAe,WAAW,OAAO,sBAAsB;;;;;;kCAE5E,6LAAC,+IAAA,CAAA,eAAY;;;;;oBACZ,OAAO,OAAO,CAAC,qHAAA,CAAA,cAAW,EAAE,GAAG,CAAC,CAAC,CAAC,MAAM,KAAK,iBAC5C,6LAAC,+IAAA,CAAA,mBAAgB;4BAEf,SAAS,IAAM,aAAa;4BAC5B,WAAW,CAAC,0FAA0F,EACpG,WAAW,OACP,qEACA,4CACJ;;8CAEF,6LAAC;oCAAK,WAAU;8CAAW,qHAAA,CAAA,cAAW,CAAC,KAAe;;;;;;8CACtD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAuB;;;;;;sDACtC,6LAAC;4CAAI,WAAU;sDACZ,SAAS,OAAO,YAAY,SAAS,OAAO,aAAa;;;;;;;;;;;;gCAG7D,WAAW,sBACV,6LAAC;oCAAI,WAAU;;;;;;;2BAhBZ;;;;;;;;;;;;;;;;;AAuBjB;GAnEgB;;QACmB,iIAAA,CAAA,iBAAc;;;KADjC", "debugId": null}}, {"offset": {"line": 729, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/app/not-found.tsx"], "sourcesContent": ["\n\"use client\"\n\nimport {\n  useEffect,\n  useState\n} from 'react'\nimport Link from 'next/link'\nimport { useRouter } from 'next/navigation'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { ThemeToggle } from '@/components/theme-toggle'\nimport { LanguageToggle } from '@/components/language-toggle'\nimport { useTranslation } from '@/hooks/useTranslation'\nimport {\n  GraduationCap,\n  FileQuestion,\n  RefreshCw,\n  Home,\n  ArrowLeft,\n  Search\n} from 'lucide-react'\n\nexport default function NotFound() {\n  const router = useRouter()\n  const { t } = useTranslation()\n  const [countdown, setCountdown] = useState(10)\n  const [isRedirecting, setIsRedirecting] = useState(false)\n\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCountdown((prev) => {\n        if (prev <= 1) {\n          return 0\n        }\n        return prev - 1\n      })\n    }, 1000)\n\n    return () => clearInterval(timer)\n  }, [])\n\n  // useEffect منفصل للتوجيه\n  useEffect(() => {\n    if (countdown === 0 && !isRedirecting) {\n      setIsRedirecting(true)\n      router.push('/')\n    }\n  }, [countdown, isRedirecting, router])\n\n  const handleGoHome = () => {\n    setIsRedirecting(true)\n    router.push('/')\n  }\n\n  const handleGoBack = () => {\n    router.back()\n  }\n\n  const handleRefresh = () => {\n    window.location.reload()\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900\">\n      {/* Header */}\n      <header className=\"bg-white dark:bg-gray-800 shadow-sm border-b\">\n        <div className=\"container mx-auto px-4 py-4 flex justify-between items-center\">\n          <div className=\"flex items-center gap-2\">\n            <GraduationCap className=\"h-8 w-8 text-blue-600 dark:text-blue-400\" />\n            <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n              منصة أزياء التخرج\n            </h1>\n          </div>\n          <div className=\"flex items-center gap-2\">\n            <LanguageToggle />\n            <ThemeToggle />\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"container mx-auto px-4 py-16 flex items-center justify-center min-h-[calc(100vh-80px)]\">\n        <Card className=\"w-full max-w-2xl\">\n          <CardContent className=\"p-8 text-center\">\n            {/* Error Icon */}\n            <div className=\"mb-8\">\n              <div className=\"mx-auto w-24 h-24 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mb-4\">\n                <FileQuestion className=\"h-12 w-12 text-red-600 dark:text-red-400\" />\n              </div>\n              <h1 className=\"text-6xl font-bold text-gray-900 dark:text-white mb-2\">\n                404\n              </h1>\n              <h2 className=\"text-2xl font-semibold text-gray-700 dark:text-gray-300 arabic-text\">\n                الصفحة غير موجودة\n              </h2>\n            </div>\n\n            {/* Error Message */}\n            <div className=\"mb-8\">\n              <p className=\"text-gray-600 dark:text-gray-400 text-lg mb-4 arabic-text\">\n                عذراً، لا يمكننا العثور على الصفحة التي تبحث عنها.\n              </p>\n              <p className=\"text-gray-500 dark:text-gray-500 arabic-text\">\n                قد تكون الصفحة قد تم نقلها أو حذفها أو أن الرابط غير صحيح.\n              </p>\n            </div>\n\n            {/* Auto Redirect Notice */}\n            {!isRedirecting && (\n              <div className=\"mb-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800\">\n                <p className=\"text-blue-700 dark:text-blue-300 arabic-text\">\n                  سيتم توجيهك تلقائياً إلى الصفحة الرئيسية خلال {countdown} ثانية\n                </p>\n              </div>\n            )}\n\n            {/* Loading State */}\n            {isRedirecting && (\n              <div className=\"mb-8 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800\">\n                <div className=\"flex items-center justify-center gap-2\">\n                  <RefreshCw className=\"h-4 w-4 animate-spin text-green-600 dark:text-green-400\" />\n                  <p className=\"text-green-700 dark:text-green-300 arabic-text\">\n                    جاري التوجيه...\n                  </p>\n                </div>\n              </div>\n            )}\n\n            {/* Action Buttons */}\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <Button \n                onClick={handleGoHome}\n                className=\"flex items-center gap-2\"\n                disabled={isRedirecting}\n              >\n                <Home className=\"h-4 w-4\" />\n                <span className=\"arabic-text\">الصفحة الرئيسية</span>\n              </Button>\n\n              <Button \n                variant=\"outline\" \n                onClick={handleGoBack}\n                className=\"flex items-center gap-2\"\n                disabled={isRedirecting}\n              >\n                <ArrowLeft className=\"h-4 w-4\" />\n                <span className=\"arabic-text\">العودة للخلف</span>\n              </Button>\n\n              <Button \n                variant=\"outline\" \n                onClick={handleRefresh}\n                className=\"flex items-center gap-2\"\n                disabled={isRedirecting}\n              >\n                <RefreshCw className=\"h-4 w-4\" />\n                <span className=\"arabic-text\">إعادة تحميل</span>\n              </Button>\n            </div>\n\n            {/* Additional Help */}\n            <div className=\"mt-8 pt-8 border-t border-gray-200 dark:border-gray-700\">\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 arabic-text\">\n                ماذا يمكنك أن تفعل؟\n              </h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\">\n                <div className=\"p-4 bg-gray-50 dark:bg-gray-800 rounded-lg\">\n                  <Search className=\"h-6 w-6 text-blue-600 dark:text-blue-400 mx-auto mb-2\" />\n                  <p className=\"text-gray-700 dark:text-gray-300 arabic-text\">\n                    ابحث عن المنتجات في الكتالوج\n                  </p>\n                  <Link href=\"/catalog\" className=\"text-blue-600 dark:text-blue-400 hover:underline\">\n                    تصفح الكتالوج\n                  </Link>\n                </div>\n\n                <div className=\"p-4 bg-gray-50 dark:bg-gray-800 rounded-lg\">\n                  <GraduationCap className=\"h-6 w-6 text-green-600 dark:text-green-400 mx-auto mb-2\" />\n                  <p className=\"text-gray-700 dark:text-gray-300 arabic-text\">\n                    تعرف على خدماتنا\n                  </p>\n                  <Link href=\"/about\" className=\"text-blue-600 dark:text-blue-400 hover:underline\">\n                    من نحن\n                  </Link>\n                </div>\n\n                <div className=\"p-4 bg-gray-50 dark:bg-gray-800 rounded-lg\">\n                  <Home className=\"h-6 w-6 text-purple-600 dark:text-purple-400 mx-auto mb-2\" />\n                  <p className=\"text-gray-700 dark:text-gray-300 arabic-text\">\n                    ابدأ من الصفحة الرئيسية\n                  </p>\n                  <Link href=\"/\" className=\"text-blue-600 dark:text-blue-400 hover:underline\">\n                    الصفحة الرئيسية\n                  </Link>\n                </div>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAbA;;;;;;;;;;AAsBe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,MAAM,QAAQ;4CAAY;oBACxB;oDAAa,CAAC;4BACZ,IAAI,QAAQ,GAAG;gCACb,OAAO;4BACT;4BACA,OAAO,OAAO;wBAChB;;gBACF;2CAAG;YAEH;sCAAO,IAAM,cAAc;;QAC7B;6BAAG,EAAE;IAEL,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,cAAc,KAAK,CAAC,eAAe;gBACrC,iBAAiB;gBACjB,OAAO,IAAI,CAAC;YACd;QACF;6BAAG;QAAC;QAAW;QAAe;KAAO;IAErC,MAAM,eAAe;QACnB,iBAAiB;QACjB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,eAAe;QACnB,OAAO,IAAI;IACb;IAEA,MAAM,gBAAgB;QACpB,OAAO,QAAQ,CAAC,MAAM;IACxB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,2NAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,6LAAC;oCAAG,WAAU;8CAAmD;;;;;;;;;;;;sCAInE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,2IAAA,CAAA,iBAAc;;;;;8CACf,6LAAC,wIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;0BAMlB,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CAErB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,iOAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;kDAE1B,6LAAC;wCAAG,WAAU;kDAAwD;;;;;;kDAGtE,6LAAC;wCAAG,WAAU;kDAAsE;;;;;;;;;;;;0CAMtF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAA4D;;;;;;kDAGzE,6LAAC;wCAAE,WAAU;kDAA+C;;;;;;;;;;;;4BAM7D,CAAC,+BACA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;;wCAA+C;wCACX;wCAAU;;;;;;;;;;;;4BAM9D,+BACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,6LAAC;4CAAE,WAAU;sDAAiD;;;;;;;;;;;;;;;;;0CAQpE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,WAAU;wCACV,UAAU;;0DAEV,6LAAC,sMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;kDAGhC,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS;wCACT,WAAU;wCACV,UAAU;;0DAEV,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,6LAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;kDAGhC,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS;wCACT,WAAU;wCACV,UAAU;;0DAEV,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,6LAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;;;;;;;0CAKlC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAuE;;;;;;kDAGrF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,6LAAC;wDAAE,WAAU;kEAA+C;;;;;;kEAG5D,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAAmD;;;;;;;;;;;;0DAKrF,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,2NAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;kEACzB,6LAAC;wDAAE,WAAU;kEAA+C;;;;;;kEAG5D,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAS,WAAU;kEAAmD;;;;;;;;;;;;0DAKnF,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,sMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;wDAAE,WAAU;kEAA+C;;;;;;kEAG5D,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAI,WAAU;kEAAmD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW9F;GApLwB;;QACP,qIAAA,CAAA,YAAS;QACV,iIAAA,CAAA,iBAAc;;;KAFN", "debugId": null}}]}
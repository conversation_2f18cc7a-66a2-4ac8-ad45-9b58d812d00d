{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/cairo_71c97244.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"cairo_71c97244-module___U0MEa__className\",\n  \"variable\": \"cairo_71c97244-module___U0MEa__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/cairo_71c97244.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Cairo%22,%22arguments%22:[{%22variable%22:%22--font-cairo%22,%22subsets%22:[%22arabic%22,%22latin%22],%22display%22:%22swap%22}],%22variableName%22:%22cairo%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Cairo', 'Cairo Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_68a01160.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_mono_68a01160-module__YLcDdW__className\",\n  \"variable\": \"geist_mono_68a01160-module__YLcDdW__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_68a01160.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist_Mono%22,%22arguments%22:[{%22variable%22:%22--font-geist-mono%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist Mono', 'Geist Mono Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,0JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,0JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,0JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/theme-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/theme-provider.tsx <module evaluation>\",\n    \"ThemeProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,mEACA", "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/theme-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/theme-provider.tsx\",\n    \"ThemeProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,+CACA", "debugId": null}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/contexts/AuthContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AuthProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/AuthContext.tsx <module evaluation>\",\n    \"AuthProvider\",\n);\nexport const useAuth = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/AuthContext.tsx <module evaluation>\",\n    \"useAuth\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,8DACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,8DACA", "debugId": null}}, {"offset": {"line": 125, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/contexts/AuthContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AuthProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/AuthContext.tsx\",\n    \"AuthProvider\",\n);\nexport const useAuth = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/AuthContext.tsx\",\n    \"useAuth\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,0CACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,0CACA", "debugId": null}}, {"offset": {"line": 143, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 153, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/contexts/NotificationContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const NotificationProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call NotificationProvider() from the server but NotificationProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/NotificationContext.tsx <module evaluation>\",\n    \"NotificationProvider\",\n);\nexport const formatNotificationTime = registerClientReference(\n    function() { throw new Error(\"Attempted to call formatNotificationTime() from the server but formatNotificationTime is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/NotificationContext.tsx <module evaluation>\",\n    \"formatNotificationTime\",\n);\nexport const getNotificationColor = registerClientReference(\n    function() { throw new Error(\"Attempted to call getNotificationColor() from the server but getNotificationColor is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/NotificationContext.tsx <module evaluation>\",\n    \"getNotificationColor\",\n);\nexport const getNotificationIcon = registerClientReference(\n    function() { throw new Error(\"Attempted to call getNotificationIcon() from the server but getNotificationIcon is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/NotificationContext.tsx <module evaluation>\",\n    \"getNotificationIcon\",\n);\nexport const useNotifications = registerClientReference(\n    function() { throw new Error(\"Attempted to call useNotifications() from the server but useNotifications is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/NotificationContext.tsx <module evaluation>\",\n    \"useNotifications\",\n);\n"], "names": [], "mappings": ";;;;;;;AAAA;;AACO,MAAM,uBAAuB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,sEACA;AAEG,MAAM,yBAAyB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxD;IAAa,MAAM,IAAI,MAAM;AAA4P,GACzR,sEACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,sEACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,sEACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,sEACA", "debugId": null}}, {"offset": {"line": 183, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/contexts/NotificationContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const NotificationProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call NotificationProvider() from the server but NotificationProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/NotificationContext.tsx\",\n    \"NotificationProvider\",\n);\nexport const formatNotificationTime = registerClientReference(\n    function() { throw new Error(\"Attempted to call formatNotificationTime() from the server but formatNotificationTime is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/NotificationContext.tsx\",\n    \"formatNotificationTime\",\n);\nexport const getNotificationColor = registerClientReference(\n    function() { throw new Error(\"Attempted to call getNotificationColor() from the server but getNotificationColor is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/NotificationContext.tsx\",\n    \"getNotificationColor\",\n);\nexport const getNotificationIcon = registerClientReference(\n    function() { throw new Error(\"Attempted to call getNotificationIcon() from the server but getNotificationIcon is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/NotificationContext.tsx\",\n    \"getNotificationIcon\",\n);\nexport const useNotifications = registerClientReference(\n    function() { throw new Error(\"Attempted to call useNotifications() from the server but useNotifications is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/NotificationContext.tsx\",\n    \"useNotifications\",\n);\n"], "names": [], "mappings": ";;;;;;;AAAA;;AACO,MAAM,uBAAuB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,kDACA;AAEG,MAAM,yBAAyB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxD;IAAa,MAAM,IAAI,MAAM;AAA4P,GACzR,kDACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,kDACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,kDACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,kDACA", "debugId": null}}, {"offset": {"line": 213, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/contexts/CartContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const CartProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call CartProvider() from the server but CartProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/CartContext.tsx <module evaluation>\",\n    \"CartProvider\",\n);\nexport const useCart = registerClientReference(\n    function() { throw new Error(\"Attempted to call useCart() from the server but useCart is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/CartContext.tsx <module evaluation>\",\n    \"useCart\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,8DACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,8DACA", "debugId": null}}, {"offset": {"line": 241, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/contexts/CartContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const CartProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call CartProvider() from the server but CartProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/CartContext.tsx\",\n    \"CartProvider\",\n);\nexport const useCart = registerClientReference(\n    function() { throw new Error(\"Attempted to call useCart() from the server but useCart is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/CartContext.tsx\",\n    \"useCart\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,0CACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,0CACA", "debugId": null}}, {"offset": {"line": 259, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 269, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/contexts/MenuContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const MenuProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call MenuProvider() from the server but MenuProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/MenuContext.tsx <module evaluation>\",\n    \"MenuProvider\",\n);\nexport const useMenu = registerClientReference(\n    function() { throw new Error(\"Attempted to call useMenu() from the server but useMenu is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/MenuContext.tsx <module evaluation>\",\n    \"useMenu\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,8DACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,8DACA", "debugId": null}}, {"offset": {"line": 287, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/contexts/MenuContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const MenuProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call MenuProvider() from the server but MenuProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/MenuContext.tsx\",\n    \"MenuProvider\",\n);\nexport const useMenu = registerClientReference(\n    function() { throw new Error(\"Attempted to call useMenu() from the server but useMenu is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/MenuContext.tsx\",\n    \"useMenu\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,0CACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,0CACA", "debugId": null}}, {"offset": {"line": 305, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 315, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/chat/LiveChat.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ChatButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call ChatButton() from the server but ChatButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/chat/LiveChat.tsx <module evaluation>\",\n    \"ChatButton\",\n);\nexport const LiveChat = registerClientReference(\n    function() { throw new Error(\"Attempted to call LiveChat() from the server but LiveChat is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/chat/LiveChat.tsx <module evaluation>\",\n    \"LiveChat\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,kEACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,kEACA", "debugId": null}}, {"offset": {"line": 333, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/chat/LiveChat.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ChatButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call ChatButton() from the server but ChatButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/chat/LiveChat.tsx\",\n    \"ChatButton\",\n);\nexport const LiveChat = registerClientReference(\n    function() { throw new Error(\"Attempted to call LiveChat() from the server but LiveChat is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/chat/LiveChat.tsx\",\n    \"LiveChat\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,8CACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,8CACA", "debugId": null}}, {"offset": {"line": 351, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 361, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/app/layout.tsx"], "sourcesContent": ["import type { <PERSON>ada<PERSON> } from \"next\";\nimport { Cairo } from \"next/font/google\";\nimport { <PERSON>eist_Mono } from \"next/font/google\";\nimport { ThemeProvider } from \"@/components/theme-provider\";\nimport { AuthProvider } from \"@/contexts/AuthContext\";\nimport { NotificationProvider } from \"@/contexts/NotificationContext\";\nimport { CartProvider } from \"@/contexts/CartContext\";\nimport { MenuProvider } from \"@/contexts/MenuContext\";\nimport { LiveChat } from \"@/components/chat/LiveChat\";\nimport { Toaster } from \"sonner\";\nimport \"./globals.css\";\n\n\nconst cairo = Cairo({\n  variable: \"--font-cairo\",\n  subsets: [\"arabic\", \"latin\"],\n  display: \"swap\",\n});\n\nconst geistMono = Geist_Mono({\n  variable: \"--font-geist-mono\",\n  subsets: [\"latin\"],\n});\n\nexport const metadata: Metadata = {\n  title: \"Graduation Toqs - منصة أزياء التخرج المغربية\",\n  description: \"أول منصة مغربية ذكية لتأجير وبيع أزياء التخرج مع ميزات التخصيص والذكاء الاصطناعي\",\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"ar\" dir=\"rtl\" suppressHydrationWarning>\n      <body\n        className={`${cairo.variable} ${geistMono.variable} antialiased font-cairo`}\n      >\n        <ThemeProvider\n          attribute=\"class\"\n          defaultTheme=\"system\"\n          enableSystem\n          disableTransitionOnChange\n        >\n          <AuthProvider>\n            <NotificationProvider>\n              <MenuProvider>\n                <CartProvider>\n                  {children}\n                </CartProvider>\n                <LiveChat />\n                <Toaster\n                  position=\"top-right\"\n                  dir=\"rtl\"\n                  richColors\n                  closeButton\n                />\n              </MenuProvider>\n            </NotificationProvider>\n          </AuthProvider>\n        </ThemeProvider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AAeO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;QAAK,KAAI;QAAM,wBAAwB;kBAChD,cAAA,8OAAC;YACC,WAAW,GAAG,yIAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,8IAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,uBAAuB,CAAC;sBAE3E,cAAA,8OAAC,uIAAA,CAAA,gBAAa;gBACZ,WAAU;gBACV,cAAa;gBACb,YAAY;gBACZ,yBAAyB;0BAEzB,cAAA,8OAAC,+HAAA,CAAA,eAAY;8BACX,cAAA,8OAAC,uIAAA,CAAA,uBAAoB;kCACnB,cAAA,8OAAC,+HAAA,CAAA,eAAY;;8CACX,8OAAC,+HAAA,CAAA,eAAY;8CACV;;;;;;8CAEH,8OAAC,sIAAA,CAAA,WAAQ;;;;;8CACT,8OAAC,wIAAA,CAAA,UAAO;oCACN,UAAS;oCACT,KAAI;oCACJ,UAAU;oCACV,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS7B", "debugId": null}}, {"offset": {"line": 465, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 473, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/sonner/dist/index.mjs/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/sonner/dist/index.mjs <module evaluation>\",\n    \"Toaster\",\n);\nexport const toast = registerClientReference(\n    function() { throw new Error(\"Attempted to call toast() from the server but toast is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/sonner/dist/index.mjs <module evaluation>\",\n    \"toast\",\n);\nexport const useSonner = registerClientReference(\n    function() { throw new Error(\"Attempted to call useSonner() from the server but useSonner is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/sonner/dist/index.mjs <module evaluation>\",\n    \"useSonner\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,oEACA;AAEG,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,oEACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,oEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 495, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/sonner/dist/index.mjs/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/sonner/dist/index.mjs\",\n    \"Toaster\",\n);\nexport const toast = registerClientReference(\n    function() { throw new Error(\"Attempted to call toast() from the server but toast is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/sonner/dist/index.mjs\",\n    \"toast\",\n);\nexport const useSonner = registerClientReference(\n    function() { throw new Error(\"Attempted to call useSonner() from the server but useSonner is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/sonner/dist/index.mjs\",\n    \"useSonner\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,gDACA;AAEG,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,gDACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,gDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 517, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}]}